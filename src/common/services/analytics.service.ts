/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Injectable } from "@nestjs/common";
import { Request } from "express";
import { IncomingMessage } from "http";
import * as useragent from "useragent";
import * as geoip from "geoip-lite";

export interface AnalyticsData {
  ip?: string;
  countryCode?: string;
  city?: string;
  region?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  browser?: string;
  os?: string;
  language?: string;
  userAgent?: string;
}

@Injectable()
export class AnalyticsService {
  constructor() {}

  // Overload for Express Request
  extractAnalyticsData(req: Request): AnalyticsData;
  // Overload for Socket.IO IncomingMessage
  extractAnalyticsData(req: IncomingMessage): AnalyticsData;
  extractAnalyticsData(req: Request | IncomingMessage): AnalyticsData {
    const ip = this.getClientIp(req);
    const geoData = this.getGeoData(ip);
    const deviceData = this.getDeviceData(req);
    const timezoneData = this.getTimezone(req, geoData);

    console.log("Analytics data:", {
      ip,
      ...geoData,
      ...deviceData,
      timezone: timezoneData.timezone,
      userAgent: req.headers["user-agent"] || "unknown",
      language:
        this.extractPrimaryLanguage(req.headers["accept-language"]) || null,
    });

    return {
      ip,
      countryCode: geoData.countryCode || undefined,
      city: geoData.city || undefined,
      region: geoData.region || undefined,
      latitude: geoData.latitude || undefined,
      longitude: geoData.longitude || undefined,
      timezone: timezoneData.timezone || undefined,
      browser: deviceData.browser || undefined,
      os: deviceData.os || undefined,
      userAgent: req.headers["user-agent"] || undefined,
      language:
        this.extractPrimaryLanguage(req.headers["accept-language"]) ||
        undefined,
    };
  }

  private getClientIp(req: Request | IncomingMessage): string {
    // For Cloudflare setups
    const cfConnectingIp = req.headers["cf-connecting-ip"] as string;
    if (cfConnectingIp) {
      return cfConnectingIp;
    }

    // For non-Cloudflare setups
    const xForwardedFor = req.headers["x-forwarded-for"] as string;
    if (xForwardedFor) {
      const forwardedIps = xForwardedFor.split(",").map((ip) => ip.trim());
      return forwardedIps[0]; // First IP is usually the client
    }

    const xRealIp = req.headers["x-real-ip"] as string;
    if (xRealIp) {
      return xRealIp;
    }

    // Fallback to direct connection IP
    return req.socket?.remoteAddress || "unknown";
  }

  private getTimezone(
    req: Request | IncomingMessage,
    geoData: any
  ): { timezone: string | null; source: string } {
    // First, try to get timezone from request headers (sent by frontend)
    const clientTimezone = req.headers["x-timezone"] as string;
    if (clientTimezone) {
      return { timezone: clientTimezone, source: "client_header" };
    }

    // Fallback: Try to determine timezone from geo data
    // Note: geoip-lite doesn't provide timezone directly, but we can map coordinates to timezone
    // For a more accurate timezone detection, you might want to use a dedicated timezone API
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    if (geoData.latitude && geoData.longitude) {
      const coordinateTimezone = this.getTimezoneFromCoordinates(
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        geoData.latitude,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        geoData.longitude
      );
      return { timezone: coordinateTimezone, source: "geo_coordinates" };
    }

    return { timezone: null, source: "none" };
  }

  private getTimezoneFromCoordinates(lat: number, lng: number): string | null {
    // Enhanced timezone mapping based on coordinates
    // This is a more comprehensive approach for common regions

    // Asia-Pacific region (more detailed)
    if (lat >= 20 && lat <= 50 && lng >= 100 && lng <= 145) {
      // China
      if (lng >= 100 && lng <= 125) {
        return "Asia/Shanghai";
      }
      // Japan
      if (lng >= 125 && lng <= 145 && lat >= 30 && lat <= 45) {
        return "Asia/Tokyo";
      }
      // Korea
      if (lng >= 125 && lng <= 130 && lat >= 33 && lat <= 43) {
        return "Asia/Seoul";
      }
      // Southeast Asia
      if (lat >= 0 && lat <= 25 && lng >= 95 && lng <= 140) {
        return "Asia/Singapore";
      }
    }

    // India/South Asia
    if (lat >= 5 && lat <= 35 && lng >= 65 && lng <= 95) {
      return "Asia/Kolkata";
    }

    // Europe
    if (lat >= 35 && lat <= 71 && lng >= -10 && lng <= 40) {
      return "Europe/London";
    }

    // North America
    if (lat >= 25 && lat <= 71 && lng >= -141 && lng <= -60) {
      if (lng >= -125 && lng <= -95) {
        return "America/Los_Angeles"; // Pacific
      } else if (lng >= -95 && lng <= -80) {
        return "America/Chicago"; // Central
      } else {
        return "America/New_York"; // Eastern
      }
    }

    // Australia
    if (lat >= -44 && lat <= -10 && lng >= 113 && lng <= 154) {
      return "Australia/Sydney";
    }

    // Fallback to UTC offset calculation based on longitude
    const timezoneOffset = Math.round(lng / 15);
    if (timezoneOffset >= -12 && timezoneOffset <= 12) {
      return `UTC${timezoneOffset >= 0 ? "+" : ""}${timezoneOffset}`;
    }

    return null;
  }

  private getGeoData(ip: string): {
    countryCode: string | null;
    city: string | null;
    region: string | null;
    latitude: number | null;
    longitude: number | null;
  } {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
      const geo: any = geoip.lookup(ip) || {};

      if (!geo || Object.keys(geo).length === 0) {
        return {
          countryCode: null,
          city: null,
          region: null,
          latitude: null,
          longitude: null,
        };
      }

      return {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
        countryCode: geo.country || null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
        city: geo.city || null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
        region: geo.region || null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
        latitude: geo.ll?.[0] || null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
        longitude: geo.ll?.[1] || null,
      };
    } catch (error) {
      console.error("Error getting geo data:", error);
      return {
        countryCode: null,
        city: null,
        region: null,
        latitude: null,
        longitude: null,
      };
    }
  }

  private getDeviceData(req: Request | IncomingMessage): {
    browser: string | null;
    os: string | null;
  } {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
      const agent: any = useragent.parse(req.headers["user-agent"] || "");

      return {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment
        browser: agent.family || null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-assignment
        os: agent.os?.toString() || null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-assignment
      };
    } catch (error) {
      console.error("Error parsing user agent:", error);
      return {
        browser: null,
        os: null,
      };
    }
  }

  private extractPrimaryLanguage(acceptLanguage?: string): string | null {
    if (!acceptLanguage) return null;

    // Parse the Accept-Language header
    // Format example: 'en-US,en;q=0.9,vi;q=0.8,fr-FR;q=0.7,fr;q=0.6'
    const languages = acceptLanguage.split(",");
    if (languages.length === 0) return null;

    // Get the first (highest priority) language
    const primaryLang = languages[0].trim().split(";")[0];

    // Return either the full code (e.g., 'en-US') or just the language part ('en')
    // depending on your preference
    return primaryLang; // For 'en-US'
    // OR return primaryLang.split('-')[0]; // For just 'en'
  }
}
