import { Types } from "mongoose";

export function buildConversationsListQuery(
  workspaceId: string,
  skip: number,
  limit: number
): any {
  return [
    {
      $match: {
        workspace: new Types.ObjectId(workspaceId),
      },
    },
    {
      $lookup: {
        from: "messages",
        localField: "latestMessage",
        foreignField: "_id",
        pipeline: [
          {
            $project: {
              conversation: 0,
              updatedAt: 0,
              __v: 0,
            },
          },
          {
            $lookup: {
              from: "users",
              localField: "user",
              foreignField: "_id",
              pipeline: [
                {
                  $project: {
                    firstName: 1,
                    lastName: 1,
                    avatar: 1,
                  },
                },
              ],
              as: "user",
            },
          },
          { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        ],
        as: "latestMessage",
      },
    },
    {
      $unwind: "$latestMessage",
    },
    {
      $lookup: {
        from: "contacts",
        localField: "contact",
        foreignField: "_id",
        pipeline: [],
        as: "contact",
      },
    },
    { $unwind: { path: "$contact", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "users",
        localField: "assignedTo",
        foreignField: "_id",
        pipeline: [
          {
            $project: {
              firstName: 1,
              lastName: 1,
              avatar: 1,
            },
          },
        ],
        as: "assignedTo",
      },
    },
    { $unwind: { path: "$assignedTo", preserveNullAndEmptyArrays: true } },
    {
      $sort: { lastActivityAt: -1 },
    },
    {
      $skip: skip,
    },
    {
      $limit: limit,
    },
    {
      $project: {
        __v: 0,
        updatedAt: 0,
        createdAt: 0,
        workspace: 0,
      },
    },
  ];
}

export function buildTotalConversationsQuery(workspaceId: string): any {
  return [
    {
      $match: {
        workspace: new Types.ObjectId(workspaceId),
      },
    },
    {
      $lookup: {
        from: "messages",
        localField: "latestMessage",
        foreignField: "_id",
        pipeline: [
          {
            $project: {
              _id: 1,
            },
          },
        ],
        as: "latestMessage",
      },
    },
    {
      $unwind: "$latestMessage",
    },
    { $group: { _id: null, count: { $sum: 1 } } },
    { $project: { _id: 0 } },
  ];
}

export function buildConversationDetailQuery(conversationId: string): any {
  return [
    {
      $match: {
        _id: new Types.ObjectId(conversationId),
      },
    },
    {
      $lookup: {
        from: "messages",
        localField: "latestMessage",
        foreignField: "_id",
        pipeline: [
          {
            $project: {
              conversation: 0,
              updatedAt: 0,
              __v: 0,
            },
          },
          {
            $lookup: {
              from: "users",
              localField: "user",
              foreignField: "_id",
              pipeline: [
                {
                  $project: {
                    firstName: 1,
                    lastName: 1,
                    avatar: 1,
                  },
                },
              ],
              as: "user",
            },
          },
          { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        ],
        as: "latestMessage",
      },
    },
    {
      $unwind: "$latestMessage",
    },
    {
      $lookup: {
        from: "contacts",
        localField: "contact",
        foreignField: "_id",
        pipeline: [],
        as: "contact",
      },
    },
    { $unwind: { path: "$contact", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "users",
        localField: "assignedTo",
        foreignField: "_id",
        pipeline: [
          {
            $project: {
              firstName: 1,
              lastName: 1,
              avatar: 1,
            },
          },
        ],
        as: "assignedTo",
      },
    },
    { $unwind: { path: "$assignedTo", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "users",
        localField: "participants",
        foreignField: "_id",
        pipeline: [
          {
            $project: {
              firstName: 1,
              lastName: 1,
              avatar: 1,
            },
          },
        ],
        as: "participants",
      },
    },
    {
      $project: {
        __v: 0,
        updatedAt: 0,
        workspace: 0,
      },
    },
  ];
}
