import { Types } from "mongoose";
import { MessageType } from "../schemas/message.schema";

export function buildMessagesListQuery(
  conversationId: string,
  skip: number,
  limit: number,
  userId?: string
): any {
  return [
    userId != null
      ? {
          $match: {
            conversation: new Types.ObjectId(conversationId),
            $or: [
              { type: { $ne: MessageType.NOTE } },
              {
                $and: [
                  { type: MessageType.NOTE },
                  { user: new Types.ObjectId(userId) },
                ],
              },
            ],
          },
        }
      : {
          $match: {
            conversation: new Types.ObjectId(conversationId),
          },
        },
    {
      $lookup: {
        from: "users",
        localField: "user",
        foreignField: "_id",
        pipeline: [
          {
            $project: {
              firstName: 1,
              lastName: 1,
              avatar: 1,
            },
          },
        ],
        as: "user",
      },
    },
    { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
    {
      $sort: { createdAt: -1 },
    },
    {
      $skip: skip,
    },
    {
      $limit: limit,
    },
    {
      $project: {
        __v: 0,
        conversation: 0,
      },
    },
  ];
}

export function buildTotalMessagesQuery(
  conversationId: string,
  userId?: string
): any {
  return [
    userId != null
      ? {
          $match: {
            conversation: new Types.ObjectId(conversationId),
            $or: [
              { type: { $ne: MessageType.NOTE } },
              {
                $and: [
                  { type: MessageType.NOTE },
                  { user: new Types.ObjectId(userId) },
                ],
              },
            ],
          },
        }
      : {
          $match: {
            conversation: new Types.ObjectId(conversationId),
          },
        },
    { $group: { _id: null, count: { $sum: 1 } } },
    { $project: { _id: 0 } },
  ];
}
