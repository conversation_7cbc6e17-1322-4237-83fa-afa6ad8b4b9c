import { Controller, Inject } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ChatServiceInterface } from "../interfaces/chat-service.interface";
import { AnalyticsService } from "src/common/services/analytics.service";
import { ContactServiceInterface } from "src/modules/contact";

@ApiTags("Chat")
@Controller("chat")
export class ChatController {
  constructor(
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface,
    private readonly analyticsService: AnalyticsService,
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface
  ) {}
}
