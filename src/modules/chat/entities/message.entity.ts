import { Types } from "mongoose";
import {
  MessageSender,
  MessageStatus,
  MessageType,
} from "../schemas/message.schema";
import { User, UserDocument } from "../../users/entities/user.entity";
import { MessageType as GraphQLMessageType } from "../graphql/types/message.types";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";

abstract class BaseMessage extends BaseEntity {
  conversation!: string;
  content!: string;
  sender!: MessageSender;
  user?: UserDocument | User | null;
  userId?: string;
  replyTo?: MessageDocument | Message | null;
  replyToId?: string;
  type!: MessageType;
  status!: MessageStatus;
  metadata?: Record<string, any>;
}

export interface MessageDocument extends BaseDocument<BaseMessage> {
  _id: Types.ObjectId;
}

export class Message extends BaseMessage {
  constructor(messageDoc?: MessageDocument, forRest?: boolean) {
    super();
    if (messageDoc) {
      super.assignFields(messageDoc);

      if (forRest == true) {
        this.content = messageDoc.content;
        this.sender = messageDoc.sender;
        this.user =
          messageDoc.user != null
            ? new User(messageDoc.user as UserDocument)
            : null;
        this.type = messageDoc.type;
        this.status = messageDoc.status;
        this.metadata = messageDoc.metadata;
      } else {
        this.conversation = messageDoc.conversation.toString();
        this.content = messageDoc.content;
        this.sender = messageDoc.sender;
        this.user = messageDoc.user;
        this.userId = (messageDoc.user as UserDocument)?._id?.toString();
        this.replyTo = messageDoc.replyTo;
        this.replyToId = (
          messageDoc.replyTo as MessageDocument
        )?._id?.toString();
        this.type = messageDoc.type;
        this.status = messageDoc.status;
        this.metadata = messageDoc.metadata;
      }
    }
  }

  toGraphQL(): GraphQLMessageType {
    const messageType = new GraphQLMessageType();
    messageType.id = this.id;
    messageType.content = this.content;
    messageType.sender = this.sender;
    messageType.userId = this.userId;
    messageType.replyToId = this.replyToId;
    messageType.type = this.type;
    messageType.status = this.status;
    messageType.metadata = this.metadata;
    messageType.createdAt = this.createdAt || new Date();
    messageType.updatedAt = this.updatedAt || new Date();
    return messageType;
  }
}
