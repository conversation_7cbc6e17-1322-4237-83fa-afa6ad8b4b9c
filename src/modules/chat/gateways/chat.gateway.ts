/* eslint-disable @typescript-eslint/restrict-template-expressions */
/* eslint-disable @typescript-eslint/no-base-to-string */
import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from "@nestjs/websockets";
import { Server } from "socket.io";
import { Inject, Logger } from "@nestjs/common";
import { ChatServiceInterface } from "../interfaces/chat-service.interface";
import { JwtService } from "@nestjs/jwt";
import { SendMessageDto } from "../dto/send-message.dto";
import {
  ConnectionHandler,
  AuthenticatedSocket,
} from "./handlers/connection.handler";
import { MessageHandler } from "./handlers/message.handler";
import { DisconnectHandler } from "./handlers/disconnect.handler";
import { TypingRedisService } from "../../../services/redis/typing.redis.service";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import {
  CLIENT_CLOSE_CONVERSATION_EVENT,
  CLIENT_GET_GUEST_CONVERSATION_EVENT,
  CLIENT_OPEN_CONVERSATION_EVENT,
  CLIENT_SEND_MESSAGE_EVENT,
  CLIENT_START_TYPING_EVENT,
  CLIENT_STOP_TYPING_EVENT,
  SERVER_UPDATED_CONVERSATION_EVENT,
  SERVER_USER_STATUS_EVENT,
  WORKSPACE_ROOM_PREFIX,
} from "./gateway.constants";
import {
  UPDATED_CONVERSATION_EVENT,
  USER_STATUS_EVENT,
} from "@app/shared/events/events.constants";
import { UserStatusEventDto } from "@app/shared/events/dto/user-status.event.dto";
import { SocketRedisService } from "src/services/redis/socket.redis.service";
import { ContactServiceInterface } from "src/modules/contact";
import { Conversation } from "../entities/conversation.entity";
import { toGlobalId } from "graphql-relay";
import { AnalyticsService } from "src/common/services/analytics.service";

@WebSocketGateway({
  namespace: "/chat",
  cors: {
    origin: [
      "https://sombes.com",
      "http://localhost:3000",
      "http://localhost:52800",
      "http://localhost:4000",
    ],
    credentials: true,
  },
})
export class ChatGateway
  implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit
{
  @WebSocketServer() server: Server;
  private readonly logger = new Logger(ChatGateway.name);
  private connectionHandler: ConnectionHandler;
  private messageHandler: MessageHandler;
  private disconnectHandler: DisconnectHandler;

  constructor(
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface,
    private readonly jwtService: JwtService,
    private readonly typingRedisService: TypingRedisService,
    private readonly socketRedisService: SocketRedisService,
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface,
    private eventEmitter: EventEmitter2,
    private readonly analyticsService: AnalyticsService
  ) {
    this.connectionHandler = new ConnectionHandler(
      jwtService,
      chatService,
      socketRedisService,
      contactService,
      analyticsService
    );
    this.disconnectHandler = new DisconnectHandler(
      chatService,
      socketRedisService
    );
  }

  afterInit() {
    this.messageHandler = new MessageHandler(
      this.chatService,
      this.typingRedisService,
      this.eventEmitter
    );
    this.logger.log("ChatGateway initialized");
  }

  async handleConnection(client: AuthenticatedSocket) {
    await this.connectionHandler.handleConnection(client);
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    await this.disconnectHandler.handleDisconnect(client);
  }

  @OnEvent(USER_STATUS_EVENT)
  async handleUserStatusEvent(data: UserStatusEventDto) {
    const userContactId = data.userId ?? data.contactId;
    if (!userContactId || !data.workspaceId) {
      return;
    }
    const socketIds = await this.socketRedisService.getSocketIds(userContactId);
    console.log(`${userContactId} has ${socketIds.length} socket ids`);

    this.server
      .to(`${WORKSPACE_ROOM_PREFIX}${data.workspaceId}`)
      .except(socketIds)
      .emit(SERVER_USER_STATUS_EVENT, data);

    this.logger.log(
      `Emit user status event to ${`${WORKSPACE_ROOM_PREFIX}${data.workspaceId}`} except ${socketIds.length} socket ids with data ${JSON.stringify(
        data
      )}`
    );
  }

  @OnEvent(UPDATED_CONVERSATION_EVENT)
  handleUpdatedConversation(data: {
    conversationId: string;
    workspaceId: string;
  }) {
    const resData = {
      conversationId: data.conversationId,
      conversationRawId: toGlobalId("Conversation", data.conversationId),
    };
    this.server
      .to(`${WORKSPACE_ROOM_PREFIX}${data.workspaceId}`)
      .emit(SERVER_UPDATED_CONVERSATION_EVENT, resData);

    this.logger.log(
      `Emit updated conversation event to ${`${WORKSPACE_ROOM_PREFIX}${data.workspaceId}`} with data ${JSON.stringify(
        resData
      )}`
    );
  }

  @SubscribeMessage(CLIENT_GET_GUEST_CONVERSATION_EVENT)
  async handleGetGuestConversation(
    @ConnectedSocket() client: AuthenticatedSocket
  ) {
    try {
      if (!client.contact?.id) {
        throw new Error("Unauthorized");
      }

      const conversation = await this.chatService.getConversationByContactId(
        client.contact?.id ?? "",
        client.workspaceId ?? ""
      );

      return {
        success: true,
        conversation: new Conversation(conversation, true),
      };
    } catch (error) {
      this.logger.error(
        `Error get guest conversation: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  @SubscribeMessage(CLIENT_SEND_MESSAGE_EVENT)
  async handleSendMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() payload: { conversationId: string; message: SendMessageDto }
  ) {
    return this.messageHandler.handleSendMessage(client, payload);
  }

  @SubscribeMessage(CLIENT_START_TYPING_EVENT)
  async handleTypingStart(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() payload: { conversationId: string }
  ) {
    return this.messageHandler.handleTypingStart(client, payload);
  }

  @SubscribeMessage(CLIENT_STOP_TYPING_EVENT)
  handleTypingStop(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() payload: { conversationId: string }
  ) {
    return this.messageHandler.handleTypingStop(client, payload);
  }

  @SubscribeMessage(CLIENT_OPEN_CONVERSATION_EVENT)
  handleOpenConversation(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() payload: { conversationId: string }
  ) {
    return this.messageHandler.handleOpenConversation(client, payload);
  }

  @SubscribeMessage(CLIENT_CLOSE_CONVERSATION_EVENT)
  handleCloseConversation(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() payload: { conversationId: string }
  ) {
    return this.messageHandler.handleCloseConversation(client, payload);
  }
}
