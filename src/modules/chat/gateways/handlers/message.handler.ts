import { Logger } from "@nestjs/common";
import { WsException } from "@nestjs/websockets";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import { SendMessageDto } from "../../dto/send-message.dto";
import { AuthenticatedSocket } from "./connection.handler";
import { TypingRedisService } from "../../../../services/redis/typing.redis.service";
import { MessageSender } from "../../schemas/message.schema";
import {
  CONVERSATION_ROOM_PREFIX,
  SERVER_MESSAGE_EVENT,
  SERVER_TYPING_EVENT,
} from "../gateway.constants";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { UPDATED_CONVERSATION_EVENT } from "@app/shared/events/events.constants";

export class MessageHandler {
  private readonly logger = new Logger(MessageHandler.name);

  constructor(
    private readonly chatService: ChatServiceInterface,
    private readonly typingRedisService: TypingRedisService,
    private eventEmitter: EventEmitter2
  ) {}

  async handleSendMessage(
    client: AuthenticatedSocket,
    payload: { conversationId: string; message: SendMessageDto }
  ) {
    try {
      if (!client.user && !client.contact) {
        throw new WsException("Unauthorized");
      }

      const { conversationId, message } = payload;

      this.logger.log(
        `${client.user?.email ?? client.contact?.guestId} send message ${JSON.stringify(payload)}`
      );

      const savedMessage = await this.chatService.sendMessage({
        ...message,
        conversationId,
        userId: client.user?.id,
        sender: client.user != null ? MessageSender.AGENT : MessageSender.GUEST,
      });

      if (client.user != null) {
        this.logger.log(
          `${client.user?.email} sent: ${JSON.stringify(savedMessage)} to ${conversationId}`
        );
      } else {
        this.logger.log(
          `${client.contact?.guestId} sent: ${JSON.stringify(savedMessage)} ${conversationId}`
        );
      }

      client
        .to(`${CONVERSATION_ROOM_PREFIX}${conversationId}`)
        .emit(SERVER_MESSAGE_EVENT, savedMessage);

      await this.chatService.updateConversation(conversationId, {
        latestMessageId: savedMessage._id.toString(),
      });
      this.logger.log(
        `update conversation ${conversationId} with latest message ${savedMessage._id.toString()}`
      );
      this.eventEmitter.emit(UPDATED_CONVERSATION_EVENT, {
        conversationId,
        workspaceId: client.workspaceId,
      });

      return { success: true, messageId: savedMessage._id.toString() };
    } catch (error) {
      this.logger.error(
        `Error sending message: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  async handleTypingStart(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id || client.contact?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email ?? client.contact?.guestId} is typing`
      );
      // Check if we can emit the typing event (debouncing)
      const canEmit = await this.typingRedisService.canEmitTypingEvent(
        userId,
        payload.conversationId
      );

      if (canEmit) {
        this.logger.log(
          `emit user_typing (isTyping: true) to ${payload.conversationId}`
        );
        client
          .to(`${CONVERSATION_ROOM_PREFIX}${payload.conversationId}`)
          .emit(SERVER_TYPING_EVENT, {
            userId: client.user?.id,
            isGuest: client.contact?.id != null,
            conversationId: payload.conversationId,
            isTyping: true,
          });
      } else {
        this.logger.log(`we can't emit user_typing event because debouncing`);
      }
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling typing start: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  handleTypingStop(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id || client.contact?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email ?? client.contact?.guestId} stop typing`
      );
      this.logger.log(
        `emit user_typing (isTyping: false) to ${payload.conversationId}`
      );

      client
        .to(`${CONVERSATION_ROOM_PREFIX}${payload.conversationId}`)
        .emit(SERVER_TYPING_EVENT, {
          userId: client.user?.id,
          isGuest: client.contact?.id != null,
          conversationId: payload.conversationId,
          isTyping: false,
        });
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling typing stop: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  async handleOpenConversation(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email} open conversation: ${payload.conversationId}`
      );
      await this.chatService.markMessagesAsRead(payload.conversationId, userId);
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling open conversation: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  async handleCloseConversation(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email} close conversation: ${payload.conversationId}`
      );
      await this.chatService.markMessagesAsRead(payload.conversationId, userId);
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling open conversation: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }
}
