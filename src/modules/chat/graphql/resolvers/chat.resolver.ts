import {
  <PERSON>sol<PERSON>,
  Query,
  Args,
  <PERSON>,
  Resolve<PERSON><PERSON>,
  Parent,
  Int,
} from "@nestjs/graphql";
import { Inject, UseGuards } from "@nestjs/common";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import {
  ConversationConnection,
  ConversationType,
} from "../types/conversation.types";
import { ConnectionArgs } from "src/modules/graphql/types/connection.args";
import { MessageConnection, MessageType } from "../types/message.types";

import { MessageLoader } from "src/modules/graphql/loaders/message.loader";
import { UserLoader } from "src/modules/graphql/loaders/user.loader";
import { UserType } from "@app/users/graphql/types/user.type";
import { ContactType } from "src/modules/contact/graphql/types/contact.type";
import { ContactLoader } from "src/modules/graphql/loaders/contact.loader";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";
import { GqlAuthGuard } from "src/modules/auth/guards/gql-auth.guard";

@Resolver(() => ConversationType)
export class ChatResolver {
  constructor(
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface,
    @Inject(UserLoader) private readonly userLoader: UserLoader,
    @Inject(MessageLoader) private readonly messageLoader: MessageLoader,
    @Inject(ContactLoader) private readonly contactLoader: ContactLoader
  ) {}

  @Query(() => ConversationConnection)
  @UseGuards(GqlAuthGuard)
  async conversations(
    @CurrentUser() user: JwtPayload,
    @Args("first", { nullable: true }) first?: number,
    @Args("after", { nullable: true }) after?: string,
    @Args("last", { nullable: true }) last?: number,
    @Args("before", { nullable: true }) before?: string
  ): Promise<ConversationConnection> {
    return this.chatService.getConversations(user.workspaceId, {
      first,
      after,
      last,
      before,
    });
  }

  //use ResolveField to query assignedTo if you user need to get assignedTo info
  //use DataLoader to fix N+1 queries, ex: you have 20 conversations, but you need one query users
  @ResolveField(() => UserType, { nullable: true })
  async assignedTo(@Parent() conv: ConversationType): Promise<UserType | null> {
    if (conv.assignedToId != null) {
      return this.userLoader.loadUserWithWorkspace({
        userId: conv.assignedToId,
        workspaceId: conv.workspaceId,
      }) as Promise<UserType>;
    } else {
      return null;
    }
  }

  @ResolveField(() => [UserType], { nullable: true })
  async participants(
    @Parent() conv: ConversationType
  ): Promise<UserType[] | null> {
    if (conv.participantsIds != null && conv.participantsIds.length > 0) {
      const users = await Promise.all(
        conv.participantsIds.map(
          (id) =>
            this.userLoader.loadUserWithWorkspace({
              userId: id,
              workspaceId: conv.workspaceId,
            }) as Promise<UserType>
        )
      );

      return users;
    } else {
      return [];
    }
  }

  @ResolveField(() => Int, { nullable: true })
  async unreadCount(
    @Parent() conv: ConversationType,
    @CurrentUser() user: JwtPayload
  ): Promise<number> {
    if (conv.lastReadTimestamps != null) {
      const count = await this.chatService.getUnreadCount(conv.id, user.sub);
      return count;
    } else {
      return 0;
    }
  }

  @ResolveField(() => MessageType, { nullable: true })
  async latestMessage(
    @Parent() conv: ConversationType
  ): Promise<MessageType | null> {
    if (conv.latestMessageId != null) {
      return this.messageLoader.batchMessages.load(
        conv.latestMessageId
      ) as Promise<MessageType>;
    } else {
      return null;
    }
  }

  @ResolveField(() => ContactType, { nullable: true })
  async contact(@Parent() conv: ConversationType): Promise<ContactType | null> {
    if (conv.contactId != null) {
      return this.contactLoader.batchContacts.load(
        conv.contactId
      ) as Promise<ContactType>;
    } else {
      return null;
    }
  }

  @Query(() => MessageConnection)
  async messages(
    @Args("conversationId", { type: () => ID }) conversationId: string,
    @Args("first", { nullable: true }) first?: number,
    @Args("after", { nullable: true }) after?: string,
    @Args("last", { nullable: true }) last?: number,
    @Args("before", { nullable: true }) before?: string
  ): Promise<MessageConnection> {
    return this.chatService.getMessages(conversationId, {
      first,
      after,
      last,
      before,
    });
  }

  @ResolveField(() => MessageType, { nullable: true })
  async replyTo(@Parent() msg: MessageType): Promise<MessageType | null> {
    if (msg.replyToId != null) {
      return this.messageLoader.batchMessages.load(
        msg.replyToId
      ) as Promise<MessageType>;
    } else {
      return null;
    }
  }
}
