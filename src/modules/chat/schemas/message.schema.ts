import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from "mongoose";
import { User } from "../../users/schemas/user.schema";
import { Conversation } from "./conversation.schema";

export enum MessageSender {
  GUEST = "guest",
  AGENT = "agent",
  SYSTEM = "system",
}

export enum MessageStatus {
  SENT = "sent",
  DELIVERED = "delivered",
  READ = "read",
  FAILED = "failed",
}

export enum MessageType {
  TEXT = "text",
  FILE = "file",
  IMAGE = "image",
  SYSTEM = "system",
  NOTE = "note",
}

@Schema({ timestamps: true })
export class Message {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Conversation",
    required: true,
  })
  conversation: Conversation;

  @Prop({ required: true })
  content: string;

  @Prop({
    type: String,
    enum: Object.values(MessageSender),
    required: true,
  })
  sender: MessageSender;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User" })
  user?: User;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "Message" })
  replyTo?: Message;

  @Prop({
    type: String,
    enum: Object.values(MessageType),
    default: MessageType.TEXT,
  })
  type: MessageType;

  @Prop({
    type: String,
    enum: Object.values(MessageStatus),
    default: MessageStatus.SENT,
  })
  status: MessageStatus;

  @Prop({ type: Object })
  metadata: Record<string, any>;
}

export const MessageSchema = SchemaFactory.createForClass(Message);

// Create indexes for faster queries
MessageSchema.index({ conversation: 1, createdAt: 1 });
MessageSchema.index({ conversation: 1, sender: 1 });
MessageSchema.index({ conversation: 1, status: 1 });
