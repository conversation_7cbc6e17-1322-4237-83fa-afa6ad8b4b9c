import {
  <PERSON>,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Inject,
  Request,
  Put,
} from "@nestjs/common";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { ContactServiceInterface } from "../interfaces/contact-service.interface";
import { CreateContactDto } from "../dto/create-contact.dto";
import { UpdateContactDto } from "../dto/update-contact.dto";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { Contact } from "../entities/contact.entity";

@ApiTags("Contacts")
@Controller("contacts")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ContactController {
  constructor(
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new contact in the workspace" })
  @ApiResponse({ status: 201, description: "Contact created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  async create(
    @Body() createDto: CreateContactDto,
    @Request() req: RequestWithUser
  ): Promise<Contact> {
    try {
      const contact = await this.contactService.createContact(
        req.user.workspaceId,
        createDto
      );
      return new Contact(contact);
    } catch (error) {
      throw new Error(error);
    }
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a contact" })
  @ApiParam({ name: "id", description: "Contact ID" })
  @ApiResponse({ status: 200, description: "Contact updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Contact not found" })
  async update(
    @Param("id") id: string,
    @Body() updateDto: UpdateContactDto,
    @Request() req: RequestWithUser
  ): Promise<Contact> {
    try {
      const contact = await this.contactService.updateContact(id, updateDto);
      return new Contact(contact);
    } catch (error) {
      throw new Error(error);
    }
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a contact" })
  @ApiParam({ name: "id", description: "Contact ID" })
  @ApiResponse({ status: 200, description: "Contact deleted successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Contact not found" })
  remove(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ): Promise<boolean> {
    return this.contactService.removeContact(id, req.user.sub);
  }
}
