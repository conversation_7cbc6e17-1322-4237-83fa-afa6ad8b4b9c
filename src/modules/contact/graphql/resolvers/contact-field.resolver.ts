/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { Resolver, ResolveField, Parent } from "@nestjs/graphql";
import { UserStatusRedisService } from "src/services/redis/user-status.redis.service";
import { ContactType } from "../types/contact.type";
import { Contact } from "../../entities/contact.entity";
import { toGlobalId } from "graphql-relay";
import { ConversationType } from "src/modules/chat/graphql/types/conversation.types";
import { Inject } from "@nestjs/common";
import { ChatServiceInterface } from "src/modules/chat/interfaces/chat-service.interface";
import { Conversation } from "src/modules/chat/entities/conversation.entity";

@Resolver(() => ContactType)
export class ContactFieldResolver {
  constructor(
    private readonly userStatusRedisService: UserStatusRedisService,
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface
  ) {}

  @ResolveField(() => String)
  id(@Parent() item: Contact) {
    return toGlobalId("Contact", item.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() item: Contact) {
    return item.id;
  }

  @ResolveField(() => Boolean, { defaultValue: false })
  async isOnline(@Parent() contact: ContactType): Promise<boolean> {
    // If no workspace ID is available, default to false
    if (!contact.workspaceId) {
      return false;
    }

    try {
      // Check online status from Redis only when isOnline field is requested
      const onlineStatus = await this.userStatusRedisService.isContactOnline(
        contact.id,
        contact.workspaceId
      );

      return onlineStatus === "online";
    } catch (error) {
      // If Redis is unavailable, default to false
      console.error("Failed to check contact online status:", error);
      return false;
    }
  }

  @ResolveField(() => [ConversationType], { nullable: true })
  async lastConversations(
    @Parent() contact: ContactType
  ): Promise<ConversationType[]> {
    // If no workspace ID is available, default to false
    if (!contact.id) {
      return [];
    }

    try {
      const conversations = await this.chatService.getConversationsByContact(
        contact.id,
        5
      );

      return conversations.map((item) => new Conversation(item).toGraphQL());
    } catch (error) {
      console.error("Failed to get conversations:", error);
      return [];
    }
  }
}
