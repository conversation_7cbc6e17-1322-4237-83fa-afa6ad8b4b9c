import { Resolver, Query, Args } from "@nestjs/graphql";
import { Inject, UseGuards } from "@nestjs/common";

import { ContactServiceInterface } from "../../interfaces/contact-service.interface";
import { GqlAuthGuard } from "src/modules/auth/guards/gql-auth.guard";
import { ContactPagination, ContactType } from "../types/contact.type";
import { PaginationArgs } from "src/modules/graphql/types/pagination.args";
import { Contact } from "../../entities/contact.entity";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";

@Resolver(() => ContactType)
export class ContactResolver {
  constructor(
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface
  ) {}

  @Query(() => ContactPagination)
  @UseGuards(GqlAuthGuard)
  async contacts(
    @Args("args") args: PaginationArgs,
    @CurrentUser() user: JwtPayload
  ): Promise<ContactPagination> {
    try {
      const workspaceId = user.workspaceId;
      const [contacts, total] = await Promise.all([
        this.contactService.getContacts(workspaceId, args),
        this.contactService.countContacts(workspaceId),
      ]);
      return {
        edges: contacts.map((contactDoc) => ({
          node: new Contact(contactDoc).toGraphQL(),
          cursor: contactDoc._id,
        })),
        pageInfo: {
          hasNextPage: false,
          hasPreviousPage: false,
          startCursor: null,
          endCursor: null,
        },
        totalCount: total,
      };
    } catch (error) {
      throw new Error(error);
    }
  }
}
