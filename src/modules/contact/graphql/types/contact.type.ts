import {
  Field,
  ID,
  ObjectType,
  registerEnumType,
  Float,
  Int,
} from "@nestjs/graphql";

import { Node } from "src/modules/graphql/types/node.interface";
import GraphQLJSON from "graphql-type-json";
import {
  ContactContext,
  ContactGender,
  CompanyInfo,
} from "../../schemas/contact.schema";
import { PaginationType } from "src/modules/graphql/types/pagination.type";
import { ConversationType } from "src/modules/chat/graphql/types/conversation.types";

registerEnumType(ContactGender, {
  name: "ContactGender",
  description: "The gender of the contact",
});

@ObjectType("ContactContext")
export class ContactContextType {
  @Field(() => String, { nullable: true })
  ip?: string;

  @Field(() => String, { nullable: true })
  country?: string;

  @Field(() => String, { nullable: true })
  city?: string;

  @Field(() => String, { nullable: true })
  region?: string;

  @Field(() => Float, { nullable: true })
  latitude?: number;

  @Field(() => Float, { nullable: true })
  longitude?: number;

  @Field(() => String, { nullable: true })
  timezone?: string;

  @Field(() => String, { nullable: true })
  browser?: string;

  @Field(() => String, { nullable: true })
  os?: string;

  @Field(() => String, { nullable: true })
  language?: string;
}

@ObjectType("CompanyInfo")
export class CompanyInfoType {
  @Field(() => String, { nullable: true })
  company?: string;

  @Field(() => String, { nullable: true })
  jobTitle?: string;

  @Field(() => String, { nullable: true })
  jobRole?: string;

  @Field(() => String, { nullable: true })
  website?: string;

  @Field(() => String, { nullable: true })
  city?: string;

  @Field(() => String, { nullable: true })
  country?: string;

  @Field(() => Int, { nullable: true })
  employees?: number;
}

@ObjectType("Contact", { implements: [Node] })
export class ContactType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => String, { nullable: true })
  guestId?: string;

  @Field(() => String, { nullable: true })
  avatar?: string;

  @Field(() => String)
  name: string;

  @Field(() => String, { nullable: true })
  email?: string;

  @Field(() => String, { nullable: true })
  phoneNumber?: string;

  @Field(() => String, { nullable: true })
  address?: string;

  @Field(() => String, { nullable: true })
  website?: string;

  @Field(() => ContactGender, { nullable: true })
  gender?: ContactGender;

  @Field(() => Boolean, { defaultValue: false })
  notification: boolean;

  @Field(() => CompanyInfoType, { nullable: true })
  companyInfo: CompanyInfo;

  @Field(() => [String], { defaultValue: [] })
  segments: string[];

  @Field(() => GraphQLJSON, { defaultValue: {} })
  metadata: Record<string, any>;

  @Field(() => ContactContextType, { nullable: true })
  context?: ContactContext;

  @Field(() => String, { nullable: true })
  notes?: string;

  @Field(() => Boolean, { defaultValue: false })
  isOnline: boolean;

  @Field(() => Date, { nullable: true })
  lastActivityAt?: Date;

  @Field(() => [ConversationType], { nullable: true })
  lastConversations?: ConversationType[];

  // Hidden field to store the workspace ID for the resolver
  workspaceId?: string;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;
}

@ObjectType()
export class ContactPagination extends PaginationType(ContactType) {}
