import { Create<PERSON>ontactDto } from "../dto/create-contact.dto";
import { ContactDocument } from "../entities/contact.entity";
import { UpdateContactDto } from "../dto/update-contact.dto";
import { PaginationArgs } from "src/modules/graphql/types/pagination.args";
import { ContactContext } from "../schemas/contact.schema";

export interface ContactServiceInterface {
  createContact(
    workspaceId: string,
    createDto: CreateContactDto
  ): Promise<ContactDocument>;

  updateContact(
    id: string,
    updateDto: UpdateContactDto
  ): Promise<ContactDocument>;

  updateContactContext(
    id: string,
    context: ContactContext
  ): Promise<ContactDocument>;

  removeContact(id: string, userId: string): Promise<boolean>;

  getContact(id: string): Promise<ContactDocument>;

  getContacts(
    workspaceId: string,
    args: PaginationArgs,
    keyword?: string
  ): Promise<ContactDocument[]>;

  countContacts(workspaceId: string, keyword?: string): Promise<number>;

  findContactsByIds(ids: string[]): Promise<ContactDocument[]>;

  createTempContact(
    workspaceId: string,
    guestId: string
  ): Promise<ContactDocument>;

  findContactByGuestId(
    workspaceId: string,
    guestId: string
  ): Promise<ContactDocument | null>;

  findContactsByEmail(email: string): Promise<ContactDocument[]>;
}
