import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { ContactServiceInterface } from "../interfaces/contact-service.interface";
import { Contact, ContactDocument } from "../entities/contact.entity";
import { CreateContactDto } from "../dto/create-contact.dto";
import { UpdateContactDto } from "../dto/update-contact.dto";
import { PaginationArgs } from "src/modules/graphql/types/pagination.args";
import { UserStatusRedisService } from "src/services/redis/user-status.redis.service";
import { ContactContext, ContactType } from "../schemas/contact.schema";

@Injectable()
export class ContactService implements ContactServiceInterface {
  constructor(
    @InjectModel("Contact")
    private contactModel: Model<Contact>,
    private readonly userStatusRedisService: UserStatusRedisService
  ) {}

  async createContact(
    workspaceId: string,
    createDto: CreateContactDto
  ): Promise<ContactDocument> {
    return this.contactModel.create({
      ...createDto,
      workspace: new Types.ObjectId(workspaceId),
    });
  }

  async updateContact(
    id: string,
    updateDto: UpdateContactDto
  ): Promise<ContactDocument> {
    const updatedContact = await this.contactModel
      .findByIdAndUpdate(id, updateDto, { new: true })
      .exec();

    if (!updatedContact) {
      throw new Error(`Contact with ID ${id} not found`);
    }

    return updatedContact;
  }

  async updateContactContext(
    id: string,
    context: ContactContext
  ): Promise<ContactDocument> {
    const updatedContact = await this.contactModel
      .findByIdAndUpdate(
        id,
        [
          {
            $set: {
              context: {
                $mergeObjects: ["$context", context],
              },
            },
          },
        ],
        { new: true }
      )
      .exec();

    if (!updatedContact) {
      throw new Error(`Contact with ID ${id} not found`);
    }

    return updatedContact;
  }

  async removeContact(id: string, userId: string): Promise<boolean> {
    const result = await this.contactModel
      .updateOne({ _id: id }, { deleted: true })
      .exec();
    return result !== null;
  }

  private buildContactQuery(
    workspaceId: string,
    keyword?: string
  ): Record<string, any> {
    const query: Record<string, any> = {
      workspace: new Types.ObjectId(workspaceId),
      type: ContactType.PERMANENT,
      deleted: { $ne: true },
    };

    if (keyword) {
      query["$or"] = [
        { name: { $regex: keyword, $options: "i" } },
        { email: { $regex: keyword, $options: "i" } },
        { phoneNumber: { $regex: keyword, $options: "i" } },
      ];
    }

    return query;
  }

  async getContacts(
    workspaceId: string,
    args: PaginationArgs,
    keyword?: string
  ): Promise<ContactDocument[]> {
    const { first, offset, last } = args;
    const limit = first ?? last ?? 10;
    const skip = offset ?? 0;

    return this.contactModel
      .find(this.buildContactQuery(workspaceId, keyword))
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .exec();
  }

  countContacts(workspaceId: string, keyword?: string): Promise<number> {
    return this.contactModel
      .find(this.buildContactQuery(workspaceId, keyword))
      .countDocuments()
      .exec();
  }

  async getContact(id: string): Promise<ContactDocument> {
    const contact = await this.contactModel.findById(id).exec();
    if (!contact) {
      throw new Error(`Contact with ID ${id} not found`);
    }
    return contact;
  }

  async findContactsByIds(ids: string[]): Promise<ContactDocument[]> {
    return this.contactModel.find({ _id: { $in: ids } }).exec();
  }

  async createTempContact(
    workspaceId: string,
    guestId: string
  ): Promise<ContactDocument> {
    const contact = await this.findContactByGuestId(workspaceId, guestId);
    if (!contact) {
      const newContact = await this.contactModel.create({
        guestId: guestId,
        workspace: new Types.ObjectId(workspaceId),
        name: "Guest",
        type: ContactType.TEMPORARY,
      });
      return newContact;
    } else {
      return contact;
    }
  }

  async findContactByGuestId(
    workspaceId: string,
    guestId: string
  ): Promise<ContactDocument | null> {
    return this.contactModel
      .findOne({
        guestId: guestId,
        workspace: new Types.ObjectId(workspaceId),
      })
      .exec();
  }

  async findContactsByEmail(email: string): Promise<ContactDocument[]> {
    return this.contactModel.find({ email }).exec();
  }
}
