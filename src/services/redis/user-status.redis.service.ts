import {
  Inject,
  Injectable,
  OnM<PERSON>ule<PERSON><PERSON><PERSON>,
  OnModuleInit,
} from "@nestjs/common";
import { REDIS_PUB, REDIS_SUB } from "./redis.constants";
import Redis from "ioredis";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { RedisClientService } from "./redis-client.service";
import { USER_STATUS_EVENT } from "@app/shared/events/events.constants";
import { UserStatusEventDto } from "@app/shared/events/dto/user-status.event.dto";

const USER_STATUS_CHANNEL = "user-status";
const USER_STATUS_PREFIX = "user-status:";
const USER_LAST_ACTIVITY_PREFIX = "user-last-activity:";
const CONTACT_STATUS_PREFIX = "contact-status:";
const CONTACT_LAST_ACTIVITY_PREFIX = "contact-last-activity:";

@Injectable()
export class UserStatusRedisService implements OnModuleInit, OnModuleDestroy {
  constructor(
    @Inject(REDIS_PUB) private readonly publisher: Redis,
    @Inject(REDIS_SUB) private readonly subscriber: Redis,
    private readonly redisClientService: RedisClientService,
    private eventEmitter: EventEmitter2
  ) {}

  async onModuleInit() {
    await this.subscriber.subscribe(USER_STATUS_CHANNEL);
    this.subscriber.on("message", (channel, message) => {
      if (channel !== USER_STATUS_CHANNEL) {
        return;
      }
      const event = JSON.parse(message) as UserStatusEventDto;
      this.eventEmitter.emit(USER_STATUS_EVENT, event);
    });
  }

  async setUserOnline(userId: string, workspaceId: string) {
    await this.redisClientService.set(
      `${USER_STATUS_PREFIX}${userId}:${workspaceId}`,
      "online"
    );
    await this.redisClientService.set(
      `${USER_LAST_ACTIVITY_PREFIX}${userId}:${workspaceId}`,
      Date.now().toString()
    );

    const event = new UserStatusEventDto({
      userId,
      workspaceId,
      isOnline: true,
      lastActivityAt: new Date(),
    });
    const payload = JSON.stringify(event);
    await this.publisher.publish(USER_STATUS_CHANNEL, payload);
  }

  async setUserOffline(userId: string, workspaceId: string) {
    await this.redisClientService.set(
      `${USER_STATUS_PREFIX}${userId}:${workspaceId}`,
      "offline"
    );
    await this.redisClientService.set(
      `${USER_LAST_ACTIVITY_PREFIX}${userId}:${workspaceId}`,
      Date.now().toString()
    );

    const event = new UserStatusEventDto({
      userId,
      workspaceId,
      isOnline: false,
      lastActivityAt: new Date(),
    });
    const payload = JSON.stringify(event);
    await this.publisher.publish(USER_STATUS_CHANNEL, payload);
  }

  isUserOnline(userId: string, workspaceId: string) {
    const key = `${USER_STATUS_PREFIX}${userId}:${workspaceId}`;
    return this.redisClientService.get(key);
  }

  getLastActivity(userId: string, workspaceId: string) {
    const key = `${USER_LAST_ACTIVITY_PREFIX}${userId}:${workspaceId}`;
    return this.redisClientService.get(key);
  }

  async setContactOnline(contactId: string, workspaceId: string) {
    await this.redisClientService.set(
      `${CONTACT_STATUS_PREFIX}${contactId}:${workspaceId}`,
      "online"
    );
    await this.redisClientService.set(
      `${CONTACT_LAST_ACTIVITY_PREFIX}${contactId}:${workspaceId}`,
      Date.now().toString()
    );

    const event = new UserStatusEventDto({
      contactId,
      workspaceId,
      isOnline: true,
      lastActivityAt: new Date(),
    });
    const payload = JSON.stringify(event);
    await this.publisher.publish(USER_STATUS_CHANNEL, payload);
  }

  async setContactOffline(contactId: string, workspaceId: string) {
    await this.redisClientService.set(
      `${CONTACT_STATUS_PREFIX}${contactId}:${workspaceId}`,
      "offline"
    );
    await this.redisClientService.set(
      `${CONTACT_LAST_ACTIVITY_PREFIX}${contactId}:${workspaceId}`,
      Date.now().toString()
    );

    const event = new UserStatusEventDto({
      contactId,
      workspaceId,
      isOnline: false,
      lastActivityAt: new Date(),
    });
    const payload = JSON.stringify(event);
    await this.publisher.publish(USER_STATUS_CHANNEL, payload);
  }

  isContactOnline(contactId: string, workspaceId: string) {
    const key = `${CONTACT_STATUS_PREFIX}${contactId}:${workspaceId}`;
    return this.redisClientService.get(key);
  }

  getLastContactActivity(contactId: string, workspaceId: string) {
    const key = `${CONTACT_LAST_ACTIVITY_PREFIX}${contactId}:${workspaceId}`;
    return this.redisClientService.get(key);
  }

  async onModuleDestroy() {
    await this.publisher.quit();
    await this.subscriber.quit();
  }
}
